"""
加速学习机制算法 - 基于注意力机制的实现
Accelerated Learning Mechanism inspired by Attention Mechanism

核心思想：
1. 知识图谱：维护学过的知识之间的关联
2. 相似性注意力：新知识自动关联到相关旧知识
3. 学习路径优化：基于历史学习效果选择最佳学习策略
4. 元学习：学习如何更好地学习
"""

import json
import math
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class Knowledge:
    """知识单元"""
    id: str
    content: str
    embedding: List[float]  # 知识的向量表示
    mastery_level: float   # 掌握程度 0-1
    learning_time: float   # 学习耗时
    connections: Dict[str, float]  # 与其他知识的关联强度

@dataclass
class LearningStrategy:
    """学习策略"""
    name: str
    effectiveness: float   # 对不同类型知识的有效性
    time_cost: float      # 时间成本
    prerequisites: List[str]  # 前置要求

class AcceleratedLearningSystem:
    """加速学习系统"""
    
    def __init__(self, embedding_dim: int = 128):
        self.embedding_dim = embedding_dim
        self.knowledge_base: Dict[str, Knowledge] = {}
        self.learning_strategies: Dict[str, LearningStrategy] = {}
        self.learning_history: List[Dict] = []
        self.meta_learning_params = {
            'attention_temperature': 0.1,
            'similarity_threshold': 0.3,
            'learning_rate_decay': 0.95
        }
        
        # 初始化基础学习策略
        self._initialize_strategies()
    
    def _initialize_strategies(self):
        """初始化基础学习策略"""
        strategies = [
            LearningStrategy("direct_memorization", 0.3, 1.0, []),
            LearningStrategy("analogy_learning", 0.8, 0.6, ["similar_knowledge"]),
            LearningStrategy("pattern_recognition", 0.9, 0.4, ["multiple_examples"]),
            LearningStrategy("incremental_building", 0.7, 0.8, ["foundation_knowledge"])
        ]
        
        for strategy in strategies:
            self.learning_strategies[strategy.name] = strategy
    
    def add_knowledge(self, knowledge_id: str, content: str,
                     initial_embedding: Optional[List[float]] = None) -> Knowledge:
        """添加新知识到系统"""
        if initial_embedding is None:
            # 简单的内容向量化（实际应用中可用更复杂的embedding）
            initial_embedding = self._simple_text_embedding(content)

        knowledge = Knowledge(
            id=knowledge_id,
            content=content,
            embedding=initial_embedding,
            mastery_level=0.0,
            learning_time=0.0,
            connections={}
        )

        self.knowledge_base[knowledge_id] = knowledge
        return knowledge

    def _simple_text_embedding(self, text: str) -> List[float]:
        """简单的文本向量化（演示用）"""
        # 实际应用中应该使用更好的embedding方法
        random.seed(hash(text) % 2**32)
        return [random.gauss(0, 1) for _ in range(self.embedding_dim)]
    
    def compute_attention_weights(self, new_knowledge: Knowledge) -> Dict[str, float]:
        """计算新知识对已有知识的注意力权重"""
        attention_weights = {}
        
        for kid, knowledge in self.knowledge_base.items():
            if kid == new_knowledge.id:
                continue
                
            # 计算相似度（余弦相似度）
            similarity = self._cosine_similarity(
                new_knowledge.embedding, 
                knowledge.embedding
            )
            
            # 考虑掌握程度的影响
            mastery_bonus = knowledge.mastery_level * 0.5
            
            # 注意力权重
            attention_score = similarity + mastery_bonus
            attention_weights[kid] = attention_score
        
        # Softmax归一化
        return self._softmax(attention_weights)
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm1 = math.sqrt(sum(a * a for a in vec1))
        norm2 = math.sqrt(sum(b * b for b in vec2))

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    
    def _softmax(self, scores: Dict[str, float]) -> Dict[str, float]:
        """Softmax归一化"""
        if not scores:
            return {}

        values = list(scores.values())
        exp_values = [math.exp(v / self.meta_learning_params['attention_temperature']) for v in values]
        sum_exp = sum(exp_values)
        softmax_values = [v / sum_exp for v in exp_values]

        return {k: v for k, v in zip(scores.keys(), softmax_values)}
    
    def select_optimal_strategy(self, new_knowledge: Knowledge, 
                              attention_weights: Dict[str, float]) -> str:
        """基于注意力权重选择最优学习策略"""
        strategy_scores = {}
        
        for strategy_name, strategy in self.learning_strategies.items():
            score = strategy.effectiveness
            
            # 根据相关知识调整策略得分
            if strategy_name == "analogy_learning":
                # 如果有高相似度的已掌握知识，类比学习效果好
                max_attention = max(attention_weights.values()) if attention_weights else 0
                score *= (1 + max_attention)
            
            elif strategy_name == "pattern_recognition":
                # 如果有多个相关知识，模式识别效果好
                relevant_count = sum(1 for w in attention_weights.values() 
                                   if w > self.meta_learning_params['similarity_threshold'])
                score *= (1 + relevant_count * 0.2)
            
            elif strategy_name == "incremental_building":
                # 如果有基础知识，增量学习效果好
                foundation_strength = sum(attention_weights.values())
                score *= (1 + foundation_strength * 0.5)
            
            # 考虑时间成本
            score /= strategy.time_cost
            
            strategy_scores[strategy_name] = score
        
        # 选择得分最高的策略
        return max(strategy_scores.items(), key=lambda x: x[1])[0]
    
    def learn_knowledge(self, knowledge_id: str) -> Dict:
        """学习指定知识"""
        if knowledge_id not in self.knowledge_base:
            raise ValueError(f"Knowledge {knowledge_id} not found")
        
        knowledge = self.knowledge_base[knowledge_id]
        
        # 计算注意力权重
        attention_weights = self.compute_attention_weights(knowledge)
        
        # 选择最优策略
        optimal_strategy = self.select_optimal_strategy(knowledge, attention_weights)
        
        # 模拟学习过程
        learning_result = self._simulate_learning(knowledge, optimal_strategy, attention_weights)
        
        # 更新知识连接
        self._update_knowledge_connections(knowledge, attention_weights)
        
        # 记录学习历史
        self.learning_history.append({
            'knowledge_id': knowledge_id,
            'strategy': optimal_strategy,
            'attention_weights': attention_weights,
            'learning_time': learning_result['time'],
            'mastery_gained': learning_result['mastery_gain']
        })
        
        # 元学习：更新策略效果
        self._update_strategy_effectiveness(optimal_strategy, learning_result)
        
        return learning_result
    
    def _simulate_learning(self, knowledge: Knowledge, strategy: str, 
                          attention_weights: Dict[str, float]) -> Dict:
        """模拟学习过程"""
        base_time = self.learning_strategies[strategy].time_cost
        base_effectiveness = self.learning_strategies[strategy].effectiveness
        
        # 基于注意力权重的加速效果
        acceleration_factor = 1.0
        if attention_weights:
            # 相关知识越多，学习越快
            total_attention = sum(attention_weights.values())
            acceleration_factor = 1.0 / (1.0 + total_attention * 0.5)
        
        # 计算实际学习时间和掌握程度提升
        actual_time = base_time * acceleration_factor
        mastery_gain = base_effectiveness * (1 + total_attention * 0.3)
        mastery_gain = min(mastery_gain, 1.0 - knowledge.mastery_level)
        
        # 更新知识状态
        knowledge.learning_time += actual_time
        knowledge.mastery_level += mastery_gain
        knowledge.mastery_level = min(knowledge.mastery_level, 1.0)
        
        return {
            'time': actual_time,
            'mastery_gain': mastery_gain,
            'acceleration_factor': acceleration_factor,
            'strategy': strategy
        }
    
    def _update_knowledge_connections(self, knowledge: Knowledge, 
                                    attention_weights: Dict[str, float]):
        """更新知识连接"""
        for kid, weight in attention_weights.items():
            if weight > self.meta_learning_params['similarity_threshold']:
                knowledge.connections[kid] = weight
                # 双向连接
                if kid in self.knowledge_base:
                    self.knowledge_base[kid].connections[knowledge.id] = weight
    
    def _update_strategy_effectiveness(self, strategy: str, learning_result: Dict):
        """基于学习结果更新策略效果（元学习）"""
        # 简单的效果更新机制
        actual_effectiveness = learning_result['mastery_gain'] / learning_result['time']
        current_effectiveness = self.learning_strategies[strategy].effectiveness
        
        # 指数移动平均更新
        decay = self.meta_learning_params['learning_rate_decay']
        new_effectiveness = decay * current_effectiveness + (1 - decay) * actual_effectiveness
        self.learning_strategies[strategy].effectiveness = new_effectiveness
    
    def get_learning_path(self, target_knowledge_ids: List[str]) -> List[str]:
        """获取优化的学习路径"""
        # 基于知识依赖关系和注意力权重计算最优学习顺序
        remaining = set(target_knowledge_ids)
        path = []
        
        while remaining:
            # 选择当前最适合学习的知识
            best_candidate = None
            best_score = -1
            
            for kid in remaining:
                if kid not in self.knowledge_base:
                    continue
                
                knowledge = self.knowledge_base[kid]
                attention_weights = self.compute_attention_weights(knowledge)
                
                # 计算学习准备度得分
                readiness_score = sum(
                    self.knowledge_base[related_id].mastery_level * weight
                    for related_id, weight in attention_weights.items()
                    if related_id in self.knowledge_base
                )
                
                if readiness_score > best_score:
                    best_score = readiness_score
                    best_candidate = kid
            
            if best_candidate:
                path.append(best_candidate)
                remaining.remove(best_candidate)
            else:
                # 如果没有找到合适的，随机选择一个
                path.append(remaining.pop())
        
        return path
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        total_knowledge = len(self.knowledge_base)
        avg_mastery = sum(k.mastery_level for k in self.knowledge_base.values()) / total_knowledge if total_knowledge > 0 else 0
        total_connections = sum(len(k.connections) for k in self.knowledge_base.values())
        
        return {
            'total_knowledge': total_knowledge,
            'average_mastery': avg_mastery,
            'total_connections': total_connections,
            'learning_sessions': len(self.learning_history),
            'strategy_effectiveness': {name: strategy.effectiveness 
                                     for name, strategy in self.learning_strategies.items()}
        }


# 使用示例
if __name__ == "__main__":
    # 创建加速学习系统
    learning_system = AcceleratedLearningSystem()
    
    # 添加一些知识
    learning_system.add_knowledge("math_basic", "基础数学：加减乘除")
    learning_system.add_knowledge("algebra", "代数：方程和不等式")
    learning_system.add_knowledge("calculus", "微积分：导数和积分")
    learning_system.add_knowledge("physics_basic", "基础物理：力学")
    learning_system.add_knowledge("physics_advanced", "高级物理：电磁学")
    
    print("=== 加速学习系统演示 ===")
    
    # 学习基础数学
    result1 = learning_system.learn_knowledge("math_basic")
    print(f"学习基础数学: 时间={result1['time']:.2f}, 掌握度提升={result1['mastery_gain']:.2f}")
    
    # 学习代数（应该会因为有数学基础而加速）
    result2 = learning_system.learn_knowledge("algebra")
    print(f"学习代数: 时间={result2['time']:.2f}, 掌握度提升={result2['mastery_gain']:.2f}, 加速因子={result2['acceleration_factor']:.2f}")
    
    # 学习微积分
    result3 = learning_system.learn_knowledge("calculus")
    print(f"学习微积分: 时间={result3['time']:.2f}, 掌握度提升={result3['mastery_gain']:.2f}, 加速因子={result3['acceleration_factor']:.2f}")
    
    # 获取学习路径建议
    path = learning_system.get_learning_path(["physics_basic", "physics_advanced"])
    print(f"推荐学习路径: {' -> '.join(path)}")
    
    # 系统统计
    stats = learning_system.get_system_stats()
    print(f"\n系统统计: {stats}")
