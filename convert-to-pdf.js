const puppeteer = require('puppeteer');
const fs = require('fs');
const marked = require('marked');
const mermaid = require('mermaid');

async function convertMarkdownToPDF(inputFile, outputFile) {
  // 读取markdown文件
  const markdown = fs.readFileSync(inputFile, 'utf8');
  
  // 转换为HTML
  const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Team Analysis Report</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 { color: #333; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .mermaid { text-align: center; }
    </style>
</head>
<body>
    ${marked.parse(markdown)}
    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>`;

  // 启动浏览器
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  
  // 设置HTML内容
  await page.setContent(html, { waitUntil: 'networkidle0' });
  
  // 等待Mermaid图表渲染完成
  await page.waitForTimeout(2000);
  
  // 生成PDF
  await page.pdf({
    path: outputFile,
    format: 'A4',
    printBackground: true,
    margin: {
      top: '20mm',
      right: '20mm',
      bottom: '20mm',
      left: '20mm'
    }
  });
  
  await browser.close();
  console.log(`PDF generated: ${outputFile}`);
}

// 使用方法
convertMarkdownToPDF('team_analysis_report.md', 'team_analysis_report.pdf');
