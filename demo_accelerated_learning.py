"""
加速学习算法演示
展示如何通过注意力机制实现学习加速
"""

from accelerated_learning import AcceleratedLearningSystem

def demo_learning_acceleration():
    """演示学习加速效果"""
    print("🚀 加速学习算法演示")
    print("=" * 50)
    
    # 创建学习系统
    system = AcceleratedLearningSystem()
    
    # 构建一个学习场景：编程技能树
    knowledge_tree = {
        "basic_syntax": "编程基础语法",
        "variables": "变量和数据类型", 
        "functions": "函数定义和调用",
        "loops": "循环结构",
        "conditions": "条件判断",
        "data_structures": "数据结构",
        "algorithms": "算法设计",
        "oop": "面向对象编程",
        "design_patterns": "设计模式",
        "web_dev": "Web开发",
        "database": "数据库操作",
        "api_design": "API设计"
    }
    
    # 添加知识到系统
    for kid, content in knowledge_tree.items():
        system.add_knowledge(kid, content)
    
    print("📚 已添加知识点:")
    for kid, content in knowledge_tree.items():
        print(f"  • {kid}: {content}")
    
    print("\n🎯 开始学习过程...")
    print("-" * 30)
    
    # 模拟学习过程
    learning_order = [
        "basic_syntax", "variables", "functions", 
        "conditions", "loops", "data_structures",
        "algorithms", "oop", "design_patterns",
        "web_dev", "database", "api_design"
    ]
    
    learning_times = []
    mastery_levels = []
    acceleration_factors = []
    
    for i, knowledge_id in enumerate(learning_order):
        result = system.learn_knowledge(knowledge_id)
        
        learning_times.append(result['time'])
        current_knowledge = system.knowledge_base[knowledge_id]
        mastery_levels.append(current_knowledge.mastery_level)
        acceleration_factors.append(result['acceleration_factor'])
        
        print(f"第{i+1}步: 学习 {knowledge_id}")
        print(f"  ⏱️  学习时间: {result['time']:.2f}")
        print(f"  📈 掌握程度: {current_knowledge.mastery_level:.2f}")
        print(f"  🚀 加速因子: {result['acceleration_factor']:.2f}")
        print(f"  🎯 使用策略: {result['strategy']}")
        
        # 显示相关知识连接
        if current_knowledge.connections:
            print(f"  🔗 关联知识: {list(current_knowledge.connections.keys())[:3]}")
        print()
    
    # 分析学习效果
    print("📊 学习效果分析:")
    print(f"  • 总学习时间: {sum(learning_times):.2f}")
    print(f"  • 平均掌握程度: {sum(mastery_levels)/len(mastery_levels):.2f}")
    print(f"  • 平均加速因子: {sum(acceleration_factors)/len(acceleration_factors):.2f}")
    print(f"  • 最大加速: {min(acceleration_factors):.2f}x")
    
    # 显示系统统计
    stats = system.get_system_stats()
    print(f"\n🔍 系统统计:")
    print(f"  • 知识总数: {stats['total_knowledge']}")
    print(f"  • 知识连接数: {stats['total_connections']}")
    print(f"  • 学习会话数: {stats['learning_sessions']}")
    
    print(f"\n🧠 策略效果:")
    for strategy, effectiveness in stats['strategy_effectiveness'].items():
        print(f"  • {strategy}: {effectiveness:.3f}")
    
    return system, learning_times, acceleration_factors

def visualize_learning_progress(learning_times, acceleration_factors):
    """可视化学习进度"""
    print("📈 学习进度分析:")
    print("   学习时间变化:")
    for i, time in enumerate(learning_times):
        bar = "█" * int(time * 10)
        print(f"     步骤{i+1:2d}: {bar} ({time:.2f})")

    print("\n   加速因子变化 (越小越快):")
    for i, factor in enumerate(acceleration_factors):
        bar = "█" * int((2-factor) * 10) if factor < 2 else ""
        print(f"     步骤{i+1:2d}: {bar} ({factor:.2f})")

def demo_learning_path_optimization():
    """演示学习路径优化"""
    print("\n🛤️  学习路径优化演示")
    print("=" * 30)
    
    system = AcceleratedLearningSystem()
    
    # 添加机器学习知识树
    ml_knowledge = {
        "math_basics": "数学基础",
        "statistics": "统计学",
        "linear_algebra": "线性代数", 
        "calculus": "微积分",
        "python": "Python编程",
        "numpy": "NumPy库",
        "pandas": "Pandas数据处理",
        "matplotlib": "数据可视化",
        "sklearn": "机器学习库",
        "deep_learning": "深度学习",
        "tensorflow": "TensorFlow框架",
        "nlp": "自然语言处理"
    }
    
    for kid, content in ml_knowledge.items():
        system.add_knowledge(kid, content)
    
    # 先学习一些基础知识
    foundation = ["math_basics", "python", "statistics"]
    for knowledge_id in foundation:
        system.learn_knowledge(knowledge_id)
        print(f"✅ 已掌握: {knowledge_id}")
    
    # 获取剩余知识的最优学习路径
    remaining = [k for k in ml_knowledge.keys() if k not in foundation]
    optimal_path = system.get_learning_path(remaining)
    
    print(f"\n🎯 推荐学习路径:")
    for i, knowledge_id in enumerate(optimal_path, 1):
        print(f"  {i}. {knowledge_id}: {ml_knowledge[knowledge_id]}")
    
    return system

if __name__ == "__main__":
    # 运行主演示
    system, times, factors = demo_learning_acceleration()
    
    # 可视化结果
    visualize_learning_progress(times, factors)
    
    # 演示路径优化
    demo_learning_path_optimization()
    
    print("\n🎉 演示完成！")
    print("💡 核心思想：通过注意力机制找到相关知识，实现学习加速")
